{"inputs": ["D:\\element\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\windows.dart", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.exp", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.lib", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.pdb", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_export.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_messenger.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_plugin_registrar.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_texture_registrar.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\icudtl.dat", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\binary_messenger_impl.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\byte_buffer_streams.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\core_implementations.cc", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\engine_method_result.cc", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_engine.cc", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_view_controller.cc", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_call.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\plugin_registrar.cc", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\readme", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\standard_codec.cc", "D:\\element\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\texture_registrar_impl.h"], "outputs": ["D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\flutter_windows.dll", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\flutter_windows.dll.exp", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\flutter_windows.dll.lib", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\flutter_windows.dll.pdb", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\flutter_export.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\flutter_messenger.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\flutter_plugin_registrar.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\flutter_texture_registrar.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\flutter_windows.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\icudtl.dat", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\binary_messenger_impl.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\byte_buffer_streams.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\core_implementations.cc", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\engine_method_result.cc", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_engine.cc", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_view_controller.cc", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_call.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\plugin_registrar.cc", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\readme", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\standard_codec.cc", "D:\\project\\vs code\\novel_app002\\novel_app\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\texture_registrar_impl.h"]}